/**
 * Utility functions for managing payments in local storage
 */

// Local storage keys
const PAYMENTS_STORAGE_KEY = 'pos_payments';
const CURRENT_ORDER_PAYMENTS_KEY = 'pos_current_order_payments';

/**
 * Get all payments for current order from local storage
 * @returns {Array} Array of payment items
 */
export const getCurrentOrderPayments = () => {
  try {
    const payments = localStorage.getItem(CURRENT_ORDER_PAYMENTS_KEY);
    return payments ? JSON.parse(payments) : [];
  } catch (error) {
    console.error('Error retrieving current order payments from local storage:', error);
    return [];
  }
};

/**
 * Save payments for current order to local storage
 * @param {Array} payments - Array of payment items
 */
export const saveCurrentOrderPayments = (payments) => {
  try {
    localStorage.setItem(CURRENT_ORDER_PAYMENTS_KEY, JSON.stringify(payments));
  } catch (error) {
    console.error('Error saving current order payments to local storage:', error);
  }
};

/**
 * Add a payment to current order
 * @param {Object} payment - Payment object to add
 * @returns {Array} Updated payments array
 */
export const addPaymentToCurrentOrder = (payment) => {
  try {
    const payments = getCurrentOrderPayments();
    
    // Create payment object with required fields
    const newPayment = {
      id: Date.now().toString(), // Simple ID generation
      paymentMethod: payment.paymentMethod || 'cash',
      amount: parseFloat(payment.amount) || 0,
      receivedAmount: parseFloat(payment.receivedAmount) || 0,
      change: parseFloat(payment.change) || 0,
      paymentType: payment.paymentType || 'cash',
      paymentReceiver: payment.paymentReceiver || '',
      paymentNote: payment.paymentNote || '',
      saleNote: payment.saleNote || '',
      staffNote: payment.staffNote || '',
      timestamp: new Date().toISOString(),
      status: 'completed'
    };

    // Add to payments array
    payments.push(newPayment);
    
    // Save to local storage
    saveCurrentOrderPayments(payments);
    
    return payments;
  } catch (error) {
    console.error('Error adding payment to current order:', error);
    return getCurrentOrderPayments();
  }
};

/**
 * Remove a payment from current order
 * @param {string} paymentId - ID of payment to remove
 * @returns {Array} Updated payments array
 */
export const removePaymentFromCurrentOrder = (paymentId) => {
  try {
    const payments = getCurrentOrderPayments();
    const updatedPayments = payments.filter(payment => payment.id !== paymentId);
    
    saveCurrentOrderPayments(updatedPayments);
    return updatedPayments;
  } catch (error) {
    console.error('Error removing payment from current order:', error);
    return getCurrentOrderPayments();
  }
};

/**
 * Clear all payments for current order
 */
export const clearCurrentOrderPayments = () => {
  try {
    localStorage.removeItem(CURRENT_ORDER_PAYMENTS_KEY);
  } catch (error) {
    console.error('Error clearing current order payments:', error);
  }
};

/**
 * Calculate total paid amount for current order
 * @returns {number} Total amount paid
 */
export const calculateTotalPaid = () => {
  try {
    const payments = getCurrentOrderPayments();
    return payments.reduce((total, payment) => total + (parseFloat(payment.amount) || 0), 0);
  } catch (error) {
    console.error('Error calculating total paid amount:', error);
    return 0;
  }
};

/**
 * Calculate remaining balance for current order
 * @param {number} orderTotal - Total order amount
 * @returns {Object} Balance information
 */
export const calculateRemainingBalance = (orderTotal) => {
  try {
    const totalPaid = calculateTotalPaid();
    const remaining = parseFloat(orderTotal) - totalPaid;
    
    return {
      orderTotal: parseFloat(orderTotal),
      totalPaid: totalPaid,
      remaining: remaining,
      isFullyPaid: remaining <= 0,
      overpaid: remaining < 0 ? Math.abs(remaining) : 0
    };
  } catch (error) {
    console.error('Error calculating remaining balance:', error);
    return {
      orderTotal: parseFloat(orderTotal) || 0,
      totalPaid: 0,
      remaining: parseFloat(orderTotal) || 0,
      isFullyPaid: false,
      overpaid: 0
    };
  }
};

/**
 * Format currency for display
 * @param {number} amount - Amount to format
 * @returns {string} Formatted currency string
 */
export const formatPaymentCurrency = (amount) => {
  try {
    return new Intl.NumberFormat('en-MY', {
      style: 'currency',
      currency: 'MYR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount || 0);
  } catch (error) {
    console.error('Error formatting currency:', error);
    return `RM ${(amount || 0).toFixed(2)}`;
  }
};

/**
 * Save completed order with payments to history
 * @param {Object} orderData - Complete order data including payments
 */
export const saveCompletedOrder = (orderData) => {
  try {
    const allOrders = localStorage.getItem(PAYMENTS_STORAGE_KEY);
    const orders = allOrders ? JSON.parse(allOrders) : [];
    
    const completedOrder = {
      ...orderData,
      id: Date.now().toString(),
      payments: getCurrentOrderPayments(),
      completedAt: new Date().toISOString(),
      status: 'completed'
    };
    
    orders.push(completedOrder);
    localStorage.setItem(PAYMENTS_STORAGE_KEY, JSON.stringify(orders));
    
    // Clear current order payments after saving
    clearCurrentOrderPayments();
    
    return completedOrder;
  } catch (error) {
    console.error('Error saving completed order:', error);
    return null;
  }
};

/**
 * Get payment history
 * @returns {Array} Array of completed orders with payments
 */
export const getPaymentHistory = () => {
  try {
    const orders = localStorage.getItem(PAYMENTS_STORAGE_KEY);
    return orders ? JSON.parse(orders) : [];
  } catch (error) {
    console.error('Error retrieving payment history:', error);
    return [];
  }
};
