/**
 * Utility functions for managing customers in local storage
 */

// Local storage keys
const CUSTOMERS_STORAGE_KEY = 'pos_customers';
const SELECTED_CUSTOMER_KEY = 'pos_selected_customer';

/**
 * Get all customers from local storage
 * @returns {Array} Array of customer objects
 */
export const getCustomers = () => {
  try {
    const customers = localStorage.getItem(CUSTOMERS_STORAGE_KEY);
    return customers ? JSON.parse(customers) : getDefaultCustomers();
  } catch (error) {
    console.error('Error retrieving customers from local storage:', error);
    return getDefaultCustomers();
  }
};

/**
 * Get default customers (including Walk-in Customer)
 * @returns {Array} Array of default customer objects
 */
export const getDefaultCustomers = () => {
  return [
    {
      id: 'walk-in',
      name: 'Walk in Customer',
      phone: '',
      email: '',
      address: '',
      isDefault: true,
      createdAt: new Date().toISOString()
    }
  ];
};

/**
 * Save customers to local storage
 * @param {Array} customers - Array of customer objects
 */
export const saveCustomers = (customers) => {
  try {
    localStorage.setItem(CUSTOMERS_STORAGE_KEY, JSON.stringify(customers));
  } catch (error) {
    console.error('Error saving customers to local storage:', error);
  }
};

/**
 * Add a new customer
 * @param {Object} customerData - Customer data object
 * @returns {Object} The created customer object
 */
export const addCustomer = (customerData) => {
  try {
    const customers = getCustomers();
    
    // Generate unique ID
    const newCustomer = {
      id: generateCustomerId(),
      name: customerData.name || '',
      phone: customerData.phone || '',
      email: customerData.email || '',
      address: customerData.address || '',
      isDefault: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    customers.push(newCustomer);
    saveCustomers(customers);
    
    return newCustomer;
  } catch (error) {
    console.error('Error adding customer:', error);
    throw error;
  }
};

/**
 * Update an existing customer
 * @param {string} customerId - Customer ID
 * @param {Object} customerData - Updated customer data
 * @returns {Object|null} Updated customer object or null if not found
 */
export const updateCustomer = (customerId, customerData) => {
  try {
    const customers = getCustomers();
    const customerIndex = customers.findIndex(customer => customer.id === customerId);
    
    if (customerIndex === -1) {
      console.warn('Customer not found:', customerId);
      return null;
    }

    // Don't allow updating default customer's core properties
    if (customers[customerIndex].isDefault) {
      console.warn('Cannot update default customer');
      return customers[customerIndex];
    }

    customers[customerIndex] = {
      ...customers[customerIndex],
      ...customerData,
      id: customerId, // Ensure ID doesn't change
      updatedAt: new Date().toISOString()
    };

    saveCustomers(customers);
    return customers[customerIndex];
  } catch (error) {
    console.error('Error updating customer:', error);
    throw error;
  }
};

/**
 * Delete a customer
 * @param {string} customerId - Customer ID to delete
 * @returns {boolean} True if deleted successfully
 */
export const deleteCustomer = (customerId) => {
  try {
    const customers = getCustomers();
    const customerIndex = customers.findIndex(customer => customer.id === customerId);
    
    if (customerIndex === -1) {
      console.warn('Customer not found:', customerId);
      return false;
    }

    // Don't allow deleting default customer
    if (customers[customerIndex].isDefault) {
      console.warn('Cannot delete default customer');
      return false;
    }

    customers.splice(customerIndex, 1);
    saveCustomers(customers);
    
    // If this was the selected customer, reset to default
    const selectedCustomer = getSelectedCustomer();
    if (selectedCustomer && selectedCustomer.id === customerId) {
      setSelectedCustomer(customers.find(c => c.isDefault) || customers[0]);
    }
    
    return true;
  } catch (error) {
    console.error('Error deleting customer:', error);
    return false;
  }
};

/**
 * Get customer by ID
 * @param {string} customerId - Customer ID
 * @returns {Object|null} Customer object or null if not found
 */
export const getCustomerById = (customerId) => {
  try {
    const customers = getCustomers();
    return customers.find(customer => customer.id === customerId) || null;
  } catch (error) {
    console.error('Error getting customer by ID:', error);
    return null;
  }
};

/**
 * Get selected customer for current order
 * @returns {Object|null} Selected customer object or null
 */
export const getSelectedCustomer = () => {
  try {
    const selectedCustomer = localStorage.getItem(SELECTED_CUSTOMER_KEY);
    if (selectedCustomer) {
      return JSON.parse(selectedCustomer);
    }
    
    // Return default customer if none selected
    const customers = getCustomers();
    return customers.find(c => c.isDefault) || customers[0] || null;
  } catch (error) {
    console.error('Error retrieving selected customer:', error);
    return null;
  }
};

/**
 * Set selected customer for current order
 * @param {Object} customer - Customer object to select
 */
export const setSelectedCustomer = (customer) => {
  try {
    localStorage.setItem(SELECTED_CUSTOMER_KEY, JSON.stringify(customer));
  } catch (error) {
    console.error('Error setting selected customer:', error);
  }
};

/**
 * Clear selected customer (reset to default)
 */
export const clearSelectedCustomer = () => {
  try {
    localStorage.removeItem(SELECTED_CUSTOMER_KEY);
  } catch (error) {
    console.error('Error clearing selected customer:', error);
  }
};

/**
 * Generate unique customer ID
 * @returns {string} Unique customer ID
 */
const generateCustomerId = () => {
  return 'cust_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

/**
 * Get customers formatted for Select component
 * @returns {Array} Array of options for Select component
 */
export const getCustomersForSelect = () => {
  try {
    const customers = getCustomers();
    return customers.map(customer => ({
      value: customer.id,
      label: customer.name,
      customer: customer
    }));
  } catch (error) {
    console.error('Error formatting customers for select:', error);
    return [];
  }
};

/**
 * Search customers by name or phone
 * @param {string} searchTerm - Search term
 * @returns {Array} Array of matching customers
 */
export const searchCustomers = (searchTerm) => {
  try {
    if (!searchTerm) return getCustomers();
    
    const customers = getCustomers();
    const term = searchTerm.toLowerCase();
    
    return customers.filter(customer => 
      customer.name.toLowerCase().includes(term) ||
      customer.phone.includes(term) ||
      customer.email.toLowerCase().includes(term)
    );
  } catch (error) {
    console.error('Error searching customers:', error);
    return [];
  }
};
