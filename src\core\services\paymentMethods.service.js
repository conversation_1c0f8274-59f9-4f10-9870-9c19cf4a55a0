/**
 * Payment Methods Service
 * Handles API calls related to payment methods and payment types
 */

import { handleApiResponse } from '../utils/api.utils';
import { apiGet } from '../utils/api.interceptor';

/**
 * Service for handling payment method-related API operations
 */
class PaymentMethodsService {
  /**
   * Get all payment methods from API
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with payment methods data
   */
  async getPaymentMethods(options = {}) {
    try {
      // Note: This endpoint might need to be confirmed with backend team
      // Common endpoints could be: /payment/methods, /transaction/paymentmethods, /company/paymentmethods
      const response = await apiGet('/transaction/paymentmethods', options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      // Return fallback payment methods if API fails
      return this.getFallbackPaymentMethods();
    }
  }

  /**
   * Get payment types from API
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with payment types data
   */
  async getPaymentTypes(options = {}) {
    try {
      // Note: This endpoint might need to be confirmed with backend team
      const response = await apiGet('/transaction/paymenttypes', options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error fetching payment types:', error);
      // Return fallback payment types if API fails
      return this.getFallbackPaymentTypes();
    }
  }

  /**
   * Get active payment methods only
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with active payment methods
   */
  async getActivePaymentMethods(options = {}) {
    try {
      const paymentMethods = await this.getPaymentMethods(options);
      
      if (!paymentMethods || !Array.isArray(paymentMethods)) {
        return this.getFallbackPaymentMethods();
      }

      // Filter only active payment methods
      return paymentMethods.filter(method => 
        method.isActive === true || method.status === 'active' || method.enabled === true
      );
    } catch (error) {
      console.error('Error fetching active payment methods:', error);
      return this.getFallbackPaymentMethods();
    }
  }

  /**
   * Transform API payment method to POS format
   * @param {Object} apiPaymentMethod - Payment method from API
   * @returns {Object} - Transformed payment method for POS
   */
  transformApiPaymentMethod(apiPaymentMethod) {
    return {
      id: apiPaymentMethod.id || apiPaymentMethod.paymentMethodId,
      value: apiPaymentMethod.code || apiPaymentMethod.methodCode || apiPaymentMethod.name?.toLowerCase(),
      label: apiPaymentMethod.name || apiPaymentMethod.displayName || apiPaymentMethod.methodName,
      icon: apiPaymentMethod.icon || apiPaymentMethod.iconUrl || this.getDefaultIcon(apiPaymentMethod.name),
      isActive: apiPaymentMethod.isActive || apiPaymentMethod.status === 'active' || apiPaymentMethod.enabled,
      description: apiPaymentMethod.description || '',
      allowsChange: apiPaymentMethod.allowsChange !== false, // Default to true unless explicitly false
      requiresAuth: apiPaymentMethod.requiresAuth || false,
      processingFee: apiPaymentMethod.processingFee || 0,
      minAmount: apiPaymentMethod.minAmount || 0,
      maxAmount: apiPaymentMethod.maxAmount || null
    };
  }

  /**
   * Transform API payment type to POS format
   * @param {Object} apiPaymentType - Payment type from API
   * @returns {Object} - Transformed payment type for POS
   */
  transformApiPaymentType(apiPaymentType) {
    return {
      id: apiPaymentType.id || apiPaymentType.paymentTypeId,
      value: apiPaymentType.code || apiPaymentType.typeCode || apiPaymentType.name?.toLowerCase(),
      label: apiPaymentType.name || apiPaymentType.displayName || apiPaymentType.typeName,
      category: apiPaymentType.category || 'general',
      isActive: apiPaymentType.isActive || apiPaymentType.status === 'active' || apiPaymentType.enabled,
      description: apiPaymentType.description || ''
    };
  }

  /**
   * Get default icon for payment method
   * @param {string} methodName - Name of the payment method
   * @returns {string} - Icon path or class
   */
  getDefaultIcon(methodName) {
    const iconMap = {
      'cash': 'assets/img/icons/cash-icon.svg',
      'card': 'assets/img/icons/card.svg',
      'credit card': 'assets/img/icons/card.svg',
      'debit card': 'assets/img/icons/card.svg',
      'cheque': 'assets/img/icons/cheque.svg',
      'check': 'assets/img/icons/cheque.svg',
      'points': 'assets/img/icons/points.svg',
      'loyalty points': 'assets/img/icons/points.svg',
      'deposit': 'assets/img/icons/deposit.svg',
      'gift card': 'assets/img/icons/giftcard.svg',
      'scan': 'assets/img/icons/scan-icon.svg',
      'qr code': 'assets/img/icons/scan-icon.svg',
      'external': 'assets/img/icons/external.svg',
      'split': 'assets/img/icons/split-bill.svg',
      'split bill': 'assets/img/icons/split-bill.svg'
    };

    const lowerName = methodName?.toLowerCase() || '';
    return iconMap[lowerName] || 'assets/img/icons/cash-icon.svg';
  }

  /**
   * Get fallback payment methods when API is unavailable
   * @returns {Array} - Default payment methods
   */
  getFallbackPaymentMethods() {
    return [
      {
        id: 'cash',
        value: 'cash',
        label: 'Cash',
        icon: 'assets/img/icons/cash-icon.svg',
        isActive: true,
        allowsChange: true,
        description: 'Cash payment'
      },
      {
        id: 'card',
        value: 'card',
        label: 'Card',
        icon: 'assets/img/icons/card.svg',
        isActive: true,
        allowsChange: false,
        description: 'Credit/Debit card payment'
      },
      {
        id: 'points',
        value: 'points',
        label: 'Points',
        icon: 'assets/img/icons/points.svg',
        isActive: true,
        allowsChange: false,
        description: 'Loyalty points payment'
      }
    ];
  }

  /**
   * Get fallback payment types when API is unavailable
   * @returns {Array} - Default payment types
   */
  getFallbackPaymentTypes() {
    return [
      { id: 'cash', value: 'cash', label: 'Cash', category: 'physical' },
      { id: 'credit', value: 'credit', label: 'Credit Card', category: 'card' },
      { id: 'debit', value: 'debit', label: 'Debit Card', category: 'card' },
      { id: 'cheque', value: 'cheque', label: 'Cheque', category: 'physical' },
      { id: 'deposit', value: 'deposit', label: 'Deposit', category: 'account' },
      { id: 'points', value: 'points', label: 'Points', category: 'loyalty' }
    ];
  }

  /**
   * Get payment methods formatted for React Select
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with payment methods formatted for select
   */
  async getPaymentMethodsForSelect(options = {}) {
    try {
      const paymentMethods = await this.getActivePaymentMethods(options);
      return paymentMethods.map(method => ({
        value: method.value,
        label: method.label
      }));
    } catch (error) {
      console.error('Error getting payment methods for select:', error);
      return this.getFallbackPaymentMethods().map(method => ({
        value: method.value,
        label: method.label
      }));
    }
  }

  /**
   * Get payment types formatted for React Select
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with payment types formatted for select
   */
  async getPaymentTypesForSelect(options = {}) {
    try {
      const paymentTypes = await this.getPaymentTypes(options);
      
      if (!paymentTypes || !Array.isArray(paymentTypes)) {
        return this.getFallbackPaymentTypes().map(type => ({
          value: type.value,
          label: type.label
        }));
      }

      return paymentTypes
        .filter(type => type.isActive !== false)
        .map(type => this.transformApiPaymentType(type))
        .map(type => ({
          value: type.value,
          label: type.label
        }));
    } catch (error) {
      console.error('Error getting payment types for select:', error);
      return this.getFallbackPaymentTypes().map(type => ({
        value: type.value,
        label: type.label
      }));
    }
  }
}

// Export singleton instance
export default new PaymentMethodsService();
