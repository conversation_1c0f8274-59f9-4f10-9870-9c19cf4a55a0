/**
 * Test file for customer storage functionality
 * This is a simple test to verify the customer storage works correctly
 */

import {
  getCustomers,
  addCustomer,
  getSelectedCustomer,
  setSelectedCustomer,
  getCustomersForSelect,
  searchCustomers
} from './customerStorage';

// Mock localStorage for testing
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

describe('Customer Storage', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
  });

  test('should return default customers when no customers in storage', () => {
    localStorageMock.getItem.mockReturnValue(null);
    
    const customers = getCustomers();
    
    expect(customers).toHaveLength(1);
    expect(customers[0].id).toBe('walk-in');
    expect(customers[0].name).toBe('Walk in Customer');
    expect(customers[0].isDefault).toBe(true);
  });

  test('should add a new customer', () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify([
      {
        id: 'walk-in',
        name: 'Walk in Customer',
        phone: '',
        email: '',
        address: '',
        isDefault: true,
        createdAt: new Date().toISOString()
      }
    ]));

    const customerData = {
      name: 'John Doe',
      phone: '+1234567890',
      email: '<EMAIL>',
      address: '123 Main St'
    };

    const newCustomer = addCustomer(customerData);

    expect(newCustomer.name).toBe('John Doe');
    expect(newCustomer.phone).toBe('+1234567890');
    expect(newCustomer.email).toBe('<EMAIL>');
    expect(newCustomer.isDefault).toBe(false);
    expect(newCustomer.id).toMatch(/^cust_/);
    expect(localStorageMock.setItem).toHaveBeenCalled();
  });

  test('should format customers for Select component', () => {
    const mockCustomers = [
      {
        id: 'walk-in',
        name: 'Walk in Customer',
        phone: '',
        email: '',
        address: '',
        isDefault: true
      },
      {
        id: 'cust_123',
        name: 'John Doe',
        phone: '+1234567890',
        email: '<EMAIL>',
        address: '123 Main St',
        isDefault: false
      }
    ];

    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockCustomers));

    const options = getCustomersForSelect();

    expect(options).toHaveLength(2);
    expect(options[0]).toEqual({
      value: 'walk-in',
      label: 'Walk in Customer',
      customer: mockCustomers[0]
    });
    expect(options[1]).toEqual({
      value: 'cust_123',
      label: 'John Doe',
      customer: mockCustomers[1]
    });
  });

  test('should search customers by name and phone', () => {
    const mockCustomers = [
      {
        id: 'walk-in',
        name: 'Walk in Customer',
        phone: '',
        email: '',
        address: '',
        isDefault: true
      },
      {
        id: 'cust_123',
        name: 'John Doe',
        phone: '+1234567890',
        email: '<EMAIL>',
        address: '123 Main St',
        isDefault: false
      },
      {
        id: 'cust_456',
        name: 'Jane Smith',
        phone: '+0987654321',
        email: '<EMAIL>',
        address: '456 Oak Ave',
        isDefault: false
      }
    ];

    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockCustomers));

    // Search by name
    const nameResults = searchCustomers('john');
    expect(nameResults).toHaveLength(1);
    expect(nameResults[0].name).toBe('John Doe');

    // Search by phone
    const phoneResults = searchCustomers('123456');
    expect(phoneResults).toHaveLength(1);
    expect(phoneResults[0].name).toBe('John Doe');

    // Search by email
    const emailResults = searchCustomers('jane@');
    expect(emailResults).toHaveLength(1);
    expect(emailResults[0].name).toBe('Jane Smith');
  });

  test('should set and get selected customer', () => {
    const customer = {
      id: 'cust_123',
      name: 'John Doe',
      phone: '+1234567890',
      email: '<EMAIL>'
    };

    setSelectedCustomer(customer);
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'pos_selected_customer',
      JSON.stringify(customer)
    );

    localStorageMock.getItem.mockReturnValue(JSON.stringify(customer));
    const selectedCustomer = getSelectedCustomer();
    expect(selectedCustomer).toEqual(customer);
  });
});
