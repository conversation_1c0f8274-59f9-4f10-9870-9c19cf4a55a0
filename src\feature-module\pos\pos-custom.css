/* Custom styles for POS page */

/* Spinning animation for refresh icon */
.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Style for product cards that are in the order list */
.product-info.active {
  border: 2px solid #28a745 !important;
  box-shadow: 0 0 10px rgba(40, 167, 69, 0.2) !important;
}

/* Make the checkmark visible only when product is in order list */
.product-info .pro-img span {
  opacity: 0;
  transition: opacity 0.3s ease;
  position: absolute;
  top: 10px;
  right: 10px;
  background: #fff;
  border-radius: 50%;
  padding: 2px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  z-index: 2;
}

.product-info .pro-img span.visible {
  opacity: 1;
}

.product-info .pro-img span i {
  color: #28a745;
  font-size: 20px;
}

/* Style for the added badge */
.product-info .added-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 2;
}

.product-info .added-badge .badge {
  font-size: 0.8rem;
  width: 24px;
  height: 24px;
  padding: 0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.product-info .added-badge .badge:hover {
  background-color: #dc3545 !important; /* Change to danger color on hover */
}

.product-info .added-badge .badge .remove-icon {
  position: absolute;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-info .added-badge .badge:hover .remove-icon {
  opacity: 1;
}

.product-info .added-badge .badge:hover .ti-check {
  opacity: 0;
}

/* Animation for when a product is clicked */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.product-info.pulse-animation {
  animation: pulse 0.3s ease;
}

/* Payment Methods Styling */
.payment-item.disabled {
  opacity: 0.5;
  cursor: not-allowed !important;
  pointer-events: none;
}

.payment-item:hover:not(.disabled) {
  background-color: #f8f9fa;
  border-color: #007bff;
}

.payment-method .spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Payment Listing Styles */
.payment-listing .payment-item-row {
  transition: all 0.2s ease;
}

.payment-listing .payment-item-row:hover {
  background-color: #e9ecef !important;
}

.payment-balance-summary {
  border: 1px solid rgba(0, 123, 255, 0.2);
}

.bg-primary-transparent {
  background-color: rgba(0, 123, 255, 0.1) !important;
}

.bg-info-transparent {
  background-color: rgba(23, 162, 184, 0.1) !important;
}

/* Loading states */
.payment-methods-loading {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Payment method icons */
.payment-item img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .payment-item {
    padding: 1rem !important;
  }

  .payment-item p {
    font-size: 12px !important;
  }
}
